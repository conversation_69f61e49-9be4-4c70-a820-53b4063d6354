'use client';

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Resume, Profile, Job } from "@/lib/types";
import { toast } from "sonner";
import { Loader2, <PERSON>rkles, FileText } from "lucide-react";
import { createTailoredResume } from "@/utils/actions/resumes/actions";
import { tailorResumeToJob } from "@/utils/actions/jobs/ai";
import { BaseResumeSelector } from "@/components/resume/management/base-resume-selector";
import { LoadingOverlay, type CreationStep } from "@/components/resume/management/loading-overlay";
import { ApiErrorDialog } from "@/components/ui/api-error-dialog";

interface TailoredResumeDialogProps {
  children: React.ReactNode;
  job: Job;
  baseResumes: Resume[];
  profile: Profile;
  onResumeCreated?: () => void;
}

/**
 * 专门为岗位申请功能设计的定制简历生成对话框
 * 简化了流程，直接基于job信息生成简历
 */
export function TailoredResumeDialog({ 
  children, 
  job, 
  baseResumes, 
  profile, 
  onResumeCreated 
}: TailoredResumeDialogProps) {
  const [open, setOpen] = useState(false);
  const [selectedBaseResume, setSelectedBaseResume] = useState<string>(baseResumes?.[0]?.id || '');
  const [isCreating, setIsCreating] = useState(false);
  const [currentStep, setCurrentStep] = useState<CreationStep>('analyzing');
  const [isBaseResumeInvalid, setIsBaseResumeInvalid] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState({ title: '', description: '' });
  const router = useRouter();

  const handleCreate = async () => {
    // 验证必填字段
    if (!selectedBaseResume) {
      setIsBaseResumeInvalid(true);
      toast.error("请选择一个基础简历以继续");
      return;
    }

    setIsCreating(true);
    setCurrentStep('analyzing');

    try {
      // 1. 准备岗位信息（从job对象直接获取）
      const jobListing = {
        position_title: job.position_title || '',
        company_name: job.company_name || '',
        location: job.location || '',
        job_type: job.job_type || '',
        work_arrangement: job.work_arrangement || '',
        salary_range: job.salary_range || '',
        description: job.description || '',
        requirements: job.requirements || '',
      };

      // 2. 获取基础简历对象
      const baseResume = baseResumes?.find(r => r.id === selectedBaseResume);
      if (!baseResume) throw new Error("未找到基础简历");

      setCurrentStep('tailoring');

      // 3. 使用AI定制简历
      let tailoredContent;
      try {
        tailoredContent = await tailorResumeToJob(baseResume, jobListing, {
          model: 'gpt-4.1-nano'
        });
      } catch (error: Error | unknown) {
        if (error instanceof Error && (
            error.message.toLowerCase().includes('api key') || 
            error.message.toLowerCase().includes('unauthorized') ||
            error.message.toLowerCase().includes('invalid key'))
        ) {
          setErrorMessage({
            title: "API 密钥错误",
            description: "您的 API 密钥有问题。请检查您的设置并重试。"
          });
        } else {
          setErrorMessage({
            title: "错误",
            description: "无法定制简历。请重试。"
          });
        }
        setShowErrorDialog(true);
        setIsCreating(false);
        return;
      }

      setCurrentStep('finalizing');

      // 4. 创建定制简历并关联job_id
      const resume = await createTailoredResume(
        baseResume,
        job.id,
        jobListing.position_title,
        jobListing.company_name,
        tailoredContent,
      );

      toast.success("定制简历创建成功！");

      // 5. 跳转到简历编辑器
      router.push(`/resumes/${resume.id}`);
      
      // 6. 关闭对话框并通知父组件
      setOpen(false);
      onResumeCreated?.();
      
    } catch (error: unknown) {
      console.error('Failed to create tailored resume:', error);
      toast.error(error instanceof Error ? error.message : "无法创建简历。请重试。");
    } finally {
      setIsCreating(false);
    }
  };

  // 重置表单当对话框打开时
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      setSelectedBaseResume(baseResumes?.[0]?.id || '');
      setIsBaseResumeInvalid(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>
          {children}
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden p-0 flex flex-col">
          <DialogTitle className="sr-only">
            为 {job.position_title} @ {job.company_name} 生成定制简历
          </DialogTitle>
          
          {/* 头部 */}
          <div className="px-6 py-4 border-b flex-shrink-0">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">生成定制简历</h3>
                <p className="text-sm text-gray-600">
                  为 {job.position_title} @ {job.company_name}
                </p>
              </div>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="px-6 py-4 flex-1 overflow-auto min-h-[400px] relative">
            {isCreating && <LoadingOverlay currentStep={currentStep} />}
            
            <div className="space-y-6">
              {/* 说明文字 */}
              <div className="text-center space-y-2">
                <p className="text-gray-600 text-sm">
                  选择一份基础简历，我们将根据岗位要求为您量身定制
                </p>
              </div>
              
              {/* 基础简历选择器 */}
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">选择基础简历</h4>
                  <BaseResumeSelector
                    baseResumes={baseResumes}
                    selectedResumeId={selectedBaseResume}
                    onResumeSelect={setSelectedBaseResume}
                    isInvalid={isBaseResumeInvalid}
                  />
                </div>
              </div>

              {/* 岗位信息预览 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">岗位信息</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div><span className="font-medium">职位：</span>{job.position_title}</div>
                  <div><span className="font-medium">公司：</span>{job.company_name}</div>
                  {job.location && <div><span className="font-medium">地点：</span>{job.location}</div>}
                  {job.salary_range && <div><span className="font-medium">薪资：</span>{job.salary_range}</div>}
                </div>
              </div>
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="px-6 py-4 border-t flex justify-end gap-3 flex-shrink-0">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isCreating}
            >
              取消
            </Button>
            <Button
              onClick={handleCreate}
              disabled={isCreating || !selectedBaseResume}
              className="min-w-[120px]"
            >
              {isCreating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <FileText className="w-4 h-4 mr-2" />
                  生成简历
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 错误对话框 */}
      <ApiErrorDialog
        isOpen={showErrorDialog}
        onClose={() => setShowErrorDialog(false)}
        title={errorMessage.title}
        description={errorMessage.description}
      />
    </>
  );
}
