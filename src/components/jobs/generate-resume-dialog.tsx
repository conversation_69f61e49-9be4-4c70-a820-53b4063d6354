'use client';

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Resume, Profile, Job } from "@/lib/types";
import { toast } from "@/hooks/use-toast";
import { Loader2, <PERSON>rkles, FileText } from "lucide-react";
import { createTailoredResume } from "@/utils/actions/resumes/actions";
import { tailorResumeToJob } from "@/utils/actions/jobs/ai";
import { formatJobListing } from "@/utils/actions/jobs/ai";
import { getBaseResumes } from "@/utils/actions/jobs/actions";
import { LoadingOverlay, type CreationStep } from "../resume/management/loading-overlay";
import { BaseResumeSelector } from "../resume/management/base-resume-selector";
import { ApiErrorDialog } from "@/components/ui/api-error-dialog";
import { cn } from "@/lib/utils";

interface GenerateResumeDialogProps {
  job: Job | null;
  isOpen: boolean;
  onClose: () => void;
  onResumeGenerated?: () => void;
  profile?: Profile;
}

/**
 * 为特定岗位生成定制简历的对话框
 * 专门用于岗位申请管理流程，自动使用岗位信息
 */
export function GenerateResumeDialog({ 
  job, 
  isOpen, 
  onClose, 
  onResumeGenerated,
  profile 
}: GenerateResumeDialogProps) {
  const [baseResumes, setBaseResumes] = useState<Resume[]>([]);
  const [selectedBaseResume, setSelectedBaseResume] = useState<string>('');
  const [isCreating, setIsCreating] = useState(false);
  const [currentStep, setCurrentStep] = useState<CreationStep>('analyzing');
  const [isBaseResumeInvalid, setIsBaseResumeInvalid] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState({ title: '', description: '' });
  const [isLoadingBaseResumes, setIsLoadingBaseResumes] = useState(true);
  const router = useRouter();

  // 获取基础简历列表
  useEffect(() => {
    async function fetchBaseResumes() {
      if (!isOpen) return;
      
      try {
        setIsLoadingBaseResumes(true);
        const resumes = await getBaseResumes();
        setBaseResumes(resumes);
        if (resumes.length > 0) {
          setSelectedBaseResume(resumes[0].id);
        }
      } catch (error) {
        console.error('获取基础简历失败:', error);
        toast({
          title: '获取基础简历失败',
          description: error instanceof Error ? error.message : '未知错误',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingBaseResumes(false);
      }
    }

    fetchBaseResumes();
  }, [isOpen]);

  // 重置状态当对话框打开/关闭时
  useEffect(() => {
    if (!isOpen) {
      setIsCreating(false);
      setCurrentStep('analyzing');
      setIsBaseResumeInvalid(false);
      setShowErrorDialog(false);
    }
  }, [isOpen]);

  const handleCreate = async () => {
    if (!job) {
      toast({
        title: "错误",
        description: "未找到岗位信息",
        variant: "destructive",
      });
      return;
    }

    // 验证必填字段
    if (!selectedBaseResume) {
      setIsBaseResumeInvalid(true);
      toast({
        title: "缺少必填字段",
        description: "请选择一个基础简历以继续。",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    setIsBaseResumeInvalid(false);

    try {
      setCurrentStep('analyzing');

      // 1. 格式化岗位信息为AI可用的格式
      const formattedJobListing = formatJobListing({
        position_title: job.position_title,
        company_name: job.company_name,
        job_description: job.job_description,
        location: job.location,
        work_mode: job.work_mode,
        employment_type: job.employment_type,
        salary_range: job.salary_range,
      });

      // 2. 获取基础简历对象
      const baseResume = baseResumes.find(r => r.id === selectedBaseResume);
      if (!baseResume) throw new Error("未找到基础简历");

      setCurrentStep('tailoring');

      // 3. 使用AI定制简历
      let tailoredContent;
      try {
        tailoredContent = await tailorResumeToJob(baseResume, formattedJobListing, {
          model: 'gpt-4.1-nano' // 使用默认模型
        });
      } catch (error: Error | unknown) {
        if (error instanceof Error && (
            error.message.toLowerCase().includes('api key') || 
            error.message.toLowerCase().includes('unauthorized') ||
            error.message.toLowerCase().includes('invalid key'))
        ) {
          setErrorMessage({
            title: "API 密钥错误",
            description: "您的 API 密钥有问题。请检查您的设置并重试。"
          });
        } else {
          setErrorMessage({
            title: "错误",
            description: "无法定制简历。请重试。"
          });
        }
        setShowErrorDialog(true);
        setIsCreating(false);
        return;
      }

      setCurrentStep('finalizing');

      // 4. 创建定制简历并绑定到岗位
      const resume = await createTailoredResume(
        baseResume,
        job.id,
        job.position_title || '',
        job.company_name || '',
        tailoredContent,
      );

      toast({
        title: "成功",
        description: "简历创建成功",
      });

      // 5. 跳转到简历编辑器
      router.push(`/resumes/${resume.id}`);
      
      // 6. 通知父组件刷新数据
      onResumeGenerated?.();
      onClose();
    } catch (error: unknown) {
      console.error('Failed to create resume:', error);
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "无法创建简历。请重试。",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    if (isCreating) return; // 创建过程中不允许关闭
    onClose();
  };

  if (!job) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
        <DialogContent 
          className="max-w-3xl max-h-[90vh] overflow-hidden p-0 flex flex-col"
          onPointerDownOutside={(e) => {
            if (isCreating) {
              e.preventDefault();
            }
          }}
        >
          {/* 标题区域 */}
          <div className="px-6 py-4 border-b flex-shrink-0">
            <div className="text-center space-y-2">
              <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 mb-1">
                <Sparkles className="w-5 h-5 text-blue-600" />
              </div>
              <DialogTitle className="text-xl font-bold text-gray-900">
                生成定制简历
              </DialogTitle>
              <DialogDescription className="text-gray-600 max-w-sm mx-auto text-sm">
                为 <span className="font-semibold">{job.position_title}</span> @ <span className="font-semibold">{job.company_name}</span> 生成定制简历
              </DialogDescription>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="px-6 py-2 min-h-[400px] relative flex-1 overflow-auto">
            {isCreating && <LoadingOverlay currentStep={currentStep} />}
            
            <div className="space-y-6">
              {/* 基础简历选择 */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-gray-900">选择基础简历</h3>
                  <p className="text-sm text-gray-600">
                    选择一份基础简历作为模板，我们将根据岗位要求进行定制优化
                  </p>
                </div>

                {isLoadingBaseResumes ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-500">加载基础简历...</span>
                  </div>
                ) : baseResumes.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 px-6 text-center border border-dashed border-gray-300 rounded-lg bg-gray-50">
                    <FileText className="h-12 w-12 text-gray-400 mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      还没有基础简历
                    </h3>
                    <p className="text-gray-600 mb-4 max-w-sm">
                      您需要先创建一份基础简历，然后才能生成定制简历
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => router.push('/resumes')}
                    >
                      前往创建基础简历
                    </Button>
                  </div>
                ) : (
                  <BaseResumeSelector
                    baseResumes={baseResumes}
                    selectedResumeId={selectedBaseResume}
                    onResumeSelect={setSelectedBaseResume}
                    isInvalid={isBaseResumeInvalid}
                  />
                )}
              </div>
            </div>
          </div>

          {/* 底部按钮区域 */}
          <div className="px-6 py-4 border-t flex-shrink-0 bg-gray-50">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isCreating}
              >
                取消
              </Button>
              
              <Button
                onClick={handleCreate}
                disabled={isCreating || !selectedBaseResume || baseResumes.length === 0}
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    生成定制简历
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 错误对话框 */}
      <ApiErrorDialog
        isOpen={showErrorDialog}
        onClose={() => setShowErrorDialog(false)}
        title={errorMessage.title}
        description={errorMessage.description}
      />
    </>
  );
}
